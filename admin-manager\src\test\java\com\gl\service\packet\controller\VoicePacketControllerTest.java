package com.gl.service.packet.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.device.vo.DeviceVo;

import com.gl.service.packet.service.VoicePacketService;
import com.gl.service.packet.vo.VoicePacketVo;
import com.gl.service.packet.vo.dto.VoicePacketDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;

import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.ActiveProfiles;

import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import com.gl.framework.security.service.PermissionService;
import org.springframework.test.web.servlet.MvcResult;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.Mockito;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * VoicePacketController单元测试类
 * 测试语音包管理控制器的所有REST端点
 * 
 * @author: 测试开发
 * @date: 2025-07-15
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = { com.gl.ManagerApplication.class,
                VoicePacketControllerTestConfig.class }, webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@DisplayName("语音包控制器测试类")
class VoicePacketControllerTest {

        @Autowired
        private MockMvc mockMvc;

        @MockBean
        private VoicePacketService voicePacketService;

        @Autowired
        private PermissionService permissionService;

        @Autowired
        private ObjectMapper objectMapper;

        private VoicePacketDto testDto;
        private List<VoicePacketVo> testVoicePacketVos;
        private List<DeviceVo> testDeviceVos;
        private Result successResult;
        private Result failResult;

        @BeforeEach
        @DisplayName("初始化测试数据")
        void setUp() {
                // 初始化测试DTO
                testDto = new VoicePacketDto();
                testDto.setId(1L);
                testDto.setShopId(100L);
                testDto.setSearchCondition("测试语音包");

                // 初始化测试语音包VO列表
                testVoicePacketVos = createTestVoicePacketVos();

                // 初始化测试设备VO列表
                testDeviceVos = createTestDeviceVos();

                // 初始化成功结果
                successResult = Result.success();
                Map<String, Object> data = new HashMap<>();
                data.put("total", 2L);
                data.put("result", testVoicePacketVos);
                successResult.setData(data);

                // 初始化失败结果
                failResult = Result.fail("操作失败");

                // 重置并配置默认权限
                Mockito.reset(permissionService);
                when(permissionService.hasPermi("packet:packet:list")).thenReturn(true);
                when(permissionService.hasPermi("packet:packet:detail")).thenReturn(true);
                when(permissionService.hasPermi("packet:packet:delete")).thenReturn(true);
                when(permissionService.hasPermi("packet:packet:zipdown")).thenReturn(true);
        }

        /**
         * 创建测试用的语音包VO列表
         */
        private List<VoicePacketVo> createTestVoicePacketVos() {
                List<VoicePacketVo> vos = new ArrayList<>();

                VoicePacketVo vo1 = new VoicePacketVo();
                vo1.setId(1L);
                vo1.setName("测试语音包1");
                vo1.setVoiceTime(30);
                vo1.setFileUrl("test/voice1.mp3");
                vo1.setNickname("用户1");
                vo1.setPhone("13800138001");
                vo1.setCreateTime(new Date());
                vo1.setShopName("测试门店1");
                vos.add(vo1);

                VoicePacketVo vo2 = new VoicePacketVo();
                vo2.setId(2L);
                vo2.setName("测试语音包2");
                vo2.setVoiceTime(45);
                vo2.setFileUrl("test/voice2.mp3");
                vo2.setNickname("用户2");
                vo2.setPhone("13800138002");
                vo2.setCreateTime(new Date());
                vo2.setShopName("测试门店2");
                vos.add(vo2);

                return vos;
        }

        /**
         * 创建测试用的设备VO列表
         */
        private List<DeviceVo> createTestDeviceVos() {
                List<DeviceVo> vos = new ArrayList<>();

                DeviceVo vo1 = new DeviceVo();
                vo1.setId(1L);
                vo1.setName("测试设备1");
                vo1.setSn("SN001");
                vos.add(vo1);

                DeviceVo vo2 = new DeviceVo();
                vo2.setId(2L);
                vo2.setName("测试设备2");
                vo2.setSn("SN002");
                vos.add(vo2);

                return vos;
        }

        // ==================== GET /packet 语音包列表测试 ====================

        @Test
        @DisplayName("测试语音包列表接口 - 正常查询场景")
        void testList_正常查询场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.list(any(VoicePacketDto.class), eq(1)))
                                .thenReturn(successResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet")
                                .param("id", "1")
                                .param("shopId", "100")
                                .param("searchCondition", "测试语音包")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data.total").value(2))
                                .andExpect(jsonPath("$.data.result").isArray())
                                .andExpect(jsonPath("$.data.result.length()").value(2))
                                .andExpect(jsonPath("$.data.result[0].id").value(1))
                                .andExpect(jsonPath("$.data.result[0].name").value("测试语音包1"))
                                .andExpect(jsonPath("$.data.result[1].id").value(2))
                                .andExpect(jsonPath("$.data.result[1].name").value("测试语音包2"));

                // 验证服务方法调用
                verify(voicePacketService).list(any(VoicePacketDto.class), eq(1));
        }

        @Test
        @DisplayName("测试语音包列表接口 - 无参数查询场景")
        void testList_无参数查询场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.list(any(VoicePacketDto.class), eq(1)))
                                .thenReturn(successResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));

                // 验证服务方法调用
                verify(voicePacketService).list(any(VoicePacketDto.class), eq(1));
        }

        @Test
        @DisplayName("测试语音包列表接口 - 无权限场景")
        void testList_无权限场景() throws Exception {
                // Given - 配置无权限
                when(permissionService.hasPermi("packet:packet:list")).thenReturn(false);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isForbidden());

                // 验证权限服务被调用
                verify(permissionService, atLeastOnce()).hasPermi("packet:packet:list");

                // 验证服务方法未被调用
                verify(voicePacketService, never()).list(any(VoicePacketDto.class), anyInt());
        }

        @Test
        @DisplayName("测试语音包列表接口 - 服务异常场景")
        void testList_服务异常场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.list(any(VoicePacketDto.class), eq(1)))
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("数据库连接异常"));

                // 验证服务方法调用
                verify(voicePacketService).list(any(VoicePacketDto.class), eq(1));
        }

        // ==================== DELETE /packet 删除语音包测试 ====================

        @Test
        @DisplayName("测试删除语音包接口 - 正常删除场景")
        void testDelete_正常删除场景() throws Exception {
                // Given - 准备测试数据
                Result deleteResult = Result.success();
                when(voicePacketService.delete(any(VoicePacketDto.class)))
                                .thenReturn(deleteResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(delete("/packet")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));

                // 验证服务方法调用
                verify(voicePacketService).delete(any(VoicePacketDto.class));
        }

        @Test

        @DisplayName("测试删除语音包接口 - 删除失败场景")
        void testDelete_删除失败场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.delete(any(VoicePacketDto.class)))
                                .thenReturn(failResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(delete("/packet")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("操作失败"));

                // 验证服务方法调用
                verify(voicePacketService).delete(any(VoicePacketDto.class));
        }

        @Test
        @DisplayName("测试删除语音包接口 - 无权限场景")
        void testDelete_无权限场景() throws Exception {
                // Given - 配置无权限
                when(permissionService.hasPermi("packet:packet:delete")).thenReturn(false);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(delete("/packet")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isForbidden());

                // 验证服务方法未被调用
                verify(voicePacketService, never()).delete(any(VoicePacketDto.class));
        }

        @Test

        @DisplayName("测试删除语音包接口 - 请求体为空场景")
        void testDelete_请求体为空场景() throws Exception {
                // When & Then - 执行请求并验证结果
                mockMvc.perform(delete("/packet")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());

                // 验证服务方法调用
                verify(voicePacketService).delete(any(VoicePacketDto.class));
        }

        @Test

        @DisplayName("测试删除语音包接口 - 服务异常场景")
        void testDelete_服务异常场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.delete(any(VoicePacketDto.class)))
                                .thenThrow(new RuntimeException("删除操作异常"));

                // When & Then - 执行请求并验证结果
                mockMvc.perform(delete("/packet")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("删除操作异常"));

                // 验证服务方法调用
                verify(voicePacketService).delete(any(VoicePacketDto.class));
        }

        // ==================== GET /packet/detail 语音包详情测试 ====================

        @Test

        @DisplayName("测试语音包详情接口 - 正常查询场景")
        void testDetail_正常查询场景() throws Exception {
                // Given - 准备测试数据
                Result detailResult = Result.success(testDeviceVos);
                when(voicePacketService.detail(1L)).thenReturn(detailResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .param("id", "1")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data").isArray())
                                .andExpect(jsonPath("$.data.length()").value(2))
                                .andExpect(jsonPath("$.data[0].id").value(1))
                                .andExpect(jsonPath("$.data[0].name").value("测试设备1"))
                                .andExpect(jsonPath("$.data[0].sn").value("SN001"))
                                .andExpect(jsonPath("$.data[1].id").value(2))
                                .andExpect(jsonPath("$.data[1].name").value("测试设备2"))
                                .andExpect(jsonPath("$.data[1].sn").value("SN002"));

                // 验证服务方法调用
                verify(voicePacketService).detail(1L);
        }

        @Test
        @DisplayName("测试语音包详情接口 - ID参数缺失场景")
        void testDetail_ID参数缺失场景() throws Exception {
                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isBadRequest());

                // 验证服务方法未被调用
                verify(voicePacketService, never()).detail(anyLong());
        }

        @Test

        @DisplayName("测试语音包详情接口 - ID参数无效场景")
        void testDetail_ID参数无效场景() throws Exception {
                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .param("id", "invalid")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isBadRequest());

                // 验证服务方法未被调用
                verify(voicePacketService, never()).detail(anyLong());
        }

        @Test
        @DisplayName("测试语音包详情接口 - 无权限场景")
        void testDetail_无权限场景() throws Exception {
                // Given - 配置无权限
                when(permissionService.hasPermi("packet:packet:detail")).thenReturn(false);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .param("id", "1")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isForbidden());

                // 验证服务方法未被调用
                verify(voicePacketService, never()).detail(anyLong());
        }

        @Test

        @DisplayName("测试语音包详情接口 - 查询结果为空场景")
        void testDetail_查询结果为空场景() throws Exception {
                // Given - 准备测试数据
                Result emptyResult = Result.success(Collections.emptyList());
                when(voicePacketService.detail(999L)).thenReturn(emptyResult);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .param("id", "999")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data").isArray())
                                .andExpect(jsonPath("$.data.length()").value(0));

                // 验证服务方法调用
                verify(voicePacketService).detail(999L);
        }

        @Test

        @DisplayName("测试语音包详情接口 - 服务异常场景")
        void testDetail_服务异常场景() throws Exception {
                // Given - 准备测试数据
                when(voicePacketService.detail(1L))
                                .thenThrow(new RuntimeException("查询详情异常"));

                // When & Then - 执行请求并验证结果
                mockMvc.perform(get("/packet/detail")
                                .param("id", "1")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("查询详情异常"));

                // 验证服务方法调用
                verify(voicePacketService).detail(1L);
        }

        // ==================== POST /packet/zipDown 语音包下载测试 ====================

        @Test

        @DisplayName("测试语音包下载接口 - 正常下载场景")
        void testPlistDownLoad_正常下载场景() throws Exception {
                // Given - 准备测试数据
                doNothing().when(voicePacketService).plistDownLoad(any(VoicePacketDto.class),
                                any(HttpServletResponse.class));

                // When & Then - 执行请求并验证结果
                MvcResult result = mockMvc.perform(post("/packet/zipDown")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andReturn();

                // 验证响应内容为空（因为是文件下载）
                assertEquals("", result.getResponse().getContentAsString());

                // 验证服务方法调用
                verify(voicePacketService).plistDownLoad(any(VoicePacketDto.class), any(HttpServletResponse.class));
        }

        @Test

        @DisplayName("测试语音包下载接口 - 下载异常场景")
        void testPlistDownLoad_下载异常场景() throws Exception {
                // Given - 准备测试数据
                doThrow(new RuntimeException("文件下载异常"))
                                .when(voicePacketService)
                                .plistDownLoad(any(VoicePacketDto.class), any(HttpServletResponse.class));

                // When & Then - 执行请求并验证结果
                mockMvc.perform(post("/packet/zipDown")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("文件下载异常"));

                // 验证服务方法调用
                verify(voicePacketService).plistDownLoad(any(VoicePacketDto.class), any(HttpServletResponse.class));
        }

        @Test
        @DisplayName("测试语音包下载接口 - 无权限场景")
        void testPlistDownLoad_无权限场景() throws Exception {
                // Given - 配置无权限
                when(permissionService.hasPermi("packet:packet:zipdown")).thenReturn(false);

                // When & Then - 执行请求并验证结果
                mockMvc.perform(post("/packet/zipDown")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDto)))
                                .andDo(print())
                                .andExpect(status().isForbidden());

                // 验证服务方法未被调用
                verify(voicePacketService, never()).plistDownLoad(any(VoicePacketDto.class),
                                any(HttpServletResponse.class));
        }

        @Test

        @DisplayName("测试语音包下载接口 - 请求体为空场景")
        void testPlistDownLoad_请求体为空场景() throws Exception {
                // Given - 准备测试数据
                doNothing().when(voicePacketService).plistDownLoad(any(VoicePacketDto.class),
                                any(HttpServletResponse.class));

                // When & Then - 执行请求并验证结果
                mockMvc.perform(post("/packet/zipDown")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());

                // 验证服务方法调用
                verify(voicePacketService).plistDownLoad(any(VoicePacketDto.class), any(HttpServletResponse.class));
        }
}
